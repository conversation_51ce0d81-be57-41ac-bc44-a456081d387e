import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:neorevv/screens/agents/agents_screen.dart';
import 'package:neorevv/screens/auth/login_screen.dart';
import 'package:neorevv/screens/dashboard/dashboard_screen.dart';
import 'theme/app_theme.dart';
import 'screens/broker/register_broker_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'NeoRevv Dashboard',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppTheme.primaryColor,
          brightness: Brightness.light,
        ),

        scaffoldBackgroundColor: AppTheme.scaffoldBgColor,
        textTheme: GoogleFonts.poppinsTextTheme(Theme.of(context).textTheme),
      ),
      home: const LoginScreen(),
      routes: {
        '/dashboard': (context) => const DashboardScreen(),
        '/register-broker': (context) => RegisterBrokerScreen(),
        '/agent-screen': (context) => const AgentsScreen(),
      },
    );
  }
}
