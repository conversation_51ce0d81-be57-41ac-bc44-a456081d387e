import 'package:flutter/material.dart';
import '/config/constants.dart';

class Responsive extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;

  const Responsive({
    Key? key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  }) : super(key: key);

  static bool showDrawer(BuildContext context) =>
      MediaQuery.of(context).size.width < sideDrawerBreakpoint;

  // Enhanced responsive breakpoint methods using updated constants
  static bool isSmallMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < 600; // Small mobile threshold

  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < mobileBreakpoint; // 800

  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width >= mobileBreakpoint && // 800
      MediaQuery.of(context).size.width < tabletBreakpoint; // 1200

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= tabletBreakpoint && // 1200
      MediaQuery.of(context).size.width < desktopBreakpoint; // 1400

  static bool isLargeDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= desktopBreakpoint; // 1400+

  static bool isExtraLargeDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= 1920; // Extra large threshold

  // Utility methods for table responsiveness
  static bool isTableCompact(BuildContext context) =>
      MediaQuery.of(context).size.width < 600; // Compact table threshold

  static bool isTableScrollable(BuildContext context) =>
      MediaQuery.of(context).size.width < 900; // Scrollable table threshold

  // Get responsive column count for grids
  static int getGridColumns(BuildContext context, {int maxColumns = 4}) {
    if (isSmallMobile(context)) return 1;
    if (isMobile(context)) return 2;
    if (isTablet(context)) return 3;
    return maxColumns;
  }

  // Get responsive font size multiplier
  static double getFontSizeMultiplier(BuildContext context) {
    if (isSmallMobile(context)) return 0.85;
    if (isMobile(context)) return 0.9;
    if (isTablet(context)) return 0.95;
    return 1.0;
  }

  // Get responsive padding
  static double getResponsivePadding(BuildContext context) {
    if (isSmallMobile(context)) return 8.0;
    if (isMobile(context)) return 12.0;
    if (isTablet(context)) return 16.0;
    return 20.0;
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    if (size.width >= tabletBreakpoint) {
      return desktop;
    } else if (size.width >= mobileBreakpoint && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}
