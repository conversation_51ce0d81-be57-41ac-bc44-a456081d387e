// Padding
const primaryLayoutPadding = 22.0;
const defaultPadding = 16.0;

//margin
const webLayoutmargin =
    40.0; // wholecontent will be dispalyed within this boundary
const defaultMargin = 16.0;

// Screen Sizes - Enhanced responsive breakpoints
const smallMobileBreakpoint = 480;
const mobileBreakpoint = 768;
const tabletBreakpoint = 1024;
const desktopBreakpoint = 1200;
const largeDesktopBreakpoint = 1440;
const extraLargeDesktopBreakpoint = 1920;

// Legacy breakpoints for backward compatibility
const sideDrawerBreakpoint = 1000;
const commissionCardBreakPoint = 1100;

// Table responsive breakpoints
const tableCompactBreakpoint = 600;
const tableScrollBreakpoint = 900;

// Assets
const imageAssetpath = 'assets/images';
const iconAssetpath = 'assets/icons';
const launcherAssetpath = 'assets/launcher';
