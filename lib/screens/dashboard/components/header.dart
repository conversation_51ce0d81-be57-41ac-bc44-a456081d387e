import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../config/app_strings.dart';
import '../../../enum/user_role.dart';
import '../../../theme/app_fonts.dart';
import '/models/user.dart';
import '../../../theme/app_theme.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import 'mobile_drawer.dart';

class Header extends HookWidget {
  final String selectedTab;
  Header({super.key, required this.selectedTab});

  final User user = User(
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "9876543210",
    image: "$iconAssetpath/agent_round.png",
    role: UserRole.broker,
  );

  //valuenortifier for tab seelection
  final ValueNotifier<String> _selectedTab = ValueNotifier<String>('');

  // Getter for the mobile drawer
  Widget get mobileDrawer =>
      MobileDrawer(user: user, selectedTab: _selectedTab);

  @override
  Widget build(BuildContext context) {
    useEffect(() {
      _selectedTab.value = selectedTab;
      return null;
    }, []);

    return ValueListenableBuilder(
      valueListenable: _selectedTab,
      builder: (context, value, child) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding / 2,
            vertical: defaultPadding / 2,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(12)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                spreadRadius: 0,
                blurRadius: 2,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final Size size = MediaQuery.of(context).size;
              final bool isVerySmallScreen = size.width < 1200;
              final bool isSmallScreen = size.width < 1400;

              return Row(
                children: [
                  // Mobile menu button
                  if (Responsive.showDrawer(context))
                    IconButton(
                      icon: const Icon(Icons.menu),
                      onPressed: () {
                        Scaffold.of(context).openDrawer();
                      },
                    ),
                  if (Responsive.showDrawer(context)) const SizedBox(width: 8),

                  // Logo
                  Flexible(
                    flex: 0,
                    child: Image.asset('$launcherAssetpath/logo.png', scale: (154 / 40)),
                  ),

                  // Desktop navigation items
                  if (!Responsive.showDrawer(context)) ...[
                    const SizedBox(width: 20),
                    Flexible(
                      flex: 2,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            _buildNavItem(
                              context,
                              dashboardTab,
                              isSelected: _selectedTab.value == dashboardTab,
                            ),
                            _buildNavItem(
                              context,
                              brokersTab,
                              isSelected: _selectedTab.value == brokersTab,
                            ),
                            _buildNavItem(
                              context,
                              agentsTab,
                              isSelected: _selectedTab.value == agentsTab,
                            ),
                            _buildNavItem(
                              context,
                              salesTab,
                              isSelected: _selectedTab.value == salesTab,
                            ),
                            _buildNavItem(
                              context,
                              commissionTab,
                              isSelected: _selectedTab.value == commissionTab,
                            ),
                            if (user.role != UserRole.agent)
                              _buildNavItem(
                                context,
                                reportsTab,
                                isSelected: _selectedTab.value == reportsTab,
                              ),
                          ],
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Right side actions - make them responsive
                    Flexible(
                      flex: 0,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Add New button - hide on very small screens
                          if (!isVerySmallScreen)
                            ElevatedButton.icon(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.primaryColor,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.symmetric(
                                  horizontal: isSmallScreen ? defaultPadding * 0.7 : defaultPadding,
                                  vertical: defaultPadding / 2,
                                ),
                              ),
                              onPressed: () {
                                _selectedTab.value = addNewButton;
                                Navigator.pushNamed(context, '/register-broker');
                              },
                              icon: const Icon(Icons.add),
                              label: Text(
                                isSmallScreen ? "Add" : addNewButton,
                                style: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                              ),
                            ),
                          if (!isVerySmallScreen) SizedBox(width: isSmallScreen ? 8 : defaultPadding),

                          // Icons - always show but smaller on small screens
                          _headerIcon(Icons.notifications_outlined, isSmall: isSmallScreen),
                          SizedBox(width: isSmallScreen ? 8 : defaultPadding),
                          _headerIcon(Icons.settings_outlined, isSmall: isSmallScreen),
                          SizedBox(width: isSmallScreen ? 8 : defaultPadding),
                        ],
                      ),
                    ),
                  ],

                  // Mobile spacer
                  if (Responsive.showDrawer(context)) const Spacer(),

                  // Profile section - make it flexible
                  Flexible(
                    flex: 0,
                    child: _buildResponsiveProfile(context),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Container _headerIcon(IconData icon, {bool isSmall = false}) {
    return Container(
      //background color-light grey, circle shape
      decoration: BoxDecoration(
        color: AppTheme.headerIconBgColor,
        shape: BoxShape.circle,
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmall ? 4.0 : 5.0),
        child: Icon(
          icon,
          size: isSmall ? 18 : 24,
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    String title, {
    bool isSelected = false,
  }) {
    final bool isTablet = Responsive.isTablet(context);
    return Visibility(
      visible: !Responsive.showDrawer(context),
      child: GestureDetector(
        onTap: () {
          _selectedTab.value = title;
          if (title == dashboardTab) {
            Navigator.pushNamed(context, '/dashboard');

            //do nothing
          } else if (title == brokersTab) {
            //navigate to brokers
          } else if (title == agentsTab) {
            Navigator.pushNamed(context, '/agent-screen');

            //navigate to agents
          } else if (title == salesTab) {
            //navigate to sales
          } else if (title == commissionTab) {
            //navigate to commission
          } else if (title == reportsTab) {
            //navigate to reports
          }
        },
        child: Container(
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(
            // color: Colors.red,
            //     width: 2,
            //   ),
            // ),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? defaultPadding / 2 : defaultPadding,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: isSelected
                    ? AppFonts.mediumTextStyle(14, color: AppTheme.primaryColor)
                    : AppFonts.mediumTextStyle(14, color: Colors.black),
              ),
              // if (isSelected)
              //   Container(
              //     // height: 2,
              //     // width: 90,
              //     child: const TriangleIndicator(),
              //   ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveProfile(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final bool isSmallScreen = size.width < 1400;
    final bool isVerySmallScreen = size.width < 1200;

    if (Responsive.isMobile(context)) {
      // Mobile profile (simplified)
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            backgroundImage: AssetImage(user.image),
            radius: 16,
          ),
          const SizedBox(width: 8),
        ],
      );
    } else {
      // Desktop/tablet profile
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            backgroundImage: AssetImage(user.image),
            radius: isSmallScreen ? 16 : 20,
          ),
          if (!isVerySmallScreen) ...[
            SizedBox(width: isSmallScreen ? 6 : defaultPadding / 2),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  user.name,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: isSmallScreen ? 12 : 14,
                  ),
                ),
                Text(
                  userRoleToString(user.role),
                  style: TextStyle(
                    fontSize: isSmallScreen ? 10 : 12,
                  ),
                ),
              ],
            ),
            Icon(
              Icons.keyboard_arrow_down,
              size: isSmallScreen ? 16 : 20,
            ),
          ],
        ],
      );
    }
  }


}
