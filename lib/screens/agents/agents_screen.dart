import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/app_strings.dart';
import '../../config/responsive.dart';
import '../../theme/app_fonts.dart';
import '/config/constants.dart';
import '/screens/dashboard/components/header.dart';
import 'components/agents_table.dart';
import '../dashboard/components/dashboard_content.dart';

import '../../theme/app_theme.dart';

class AgentsScreen extends HookWidget {
  const AgentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final header = Header(selectedTab: agentsTab);
    final bool isTablet = Responsive.isTablet(context);
    final bool isMobile = Responsive.isMobile(context);
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final double responsivePadding = Responsive.getResponsivePadding(context);

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: SingleChildScrollView(
          primary: false,
          padding: EdgeInsets.symmetric(
            horizontal: isSmallMobile ? 4 : (isMobile ? 8 : webLayoutmargin),
            vertical: isSmallMobile ? 4 : (isMobile ? 8 : defaultMargin),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(right: isTablet ? defaultMargin : 0),
                child: header,
              ),
              SizedBox(height: responsivePadding),
              _welcomeUser(context),
              SizedBox(height: responsivePadding),
              const AgentsTable(),
              SizedBox(height: responsivePadding),
              const Footer(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _welcomeUser(BuildContext context) {
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);
    final double baseFontSize = 22 * fontSizeMultiplier;

    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        textAlign: TextAlign.left,
        text: TextSpan(
          text: welcomeLabel,
          style: AppFonts.normalTextStyle(
            baseFontSize,
            color: AppTheme.primaryTextColor.withValues(alpha: 0.7),
          ),
          children: [
            TextSpan(
              text: 'Nabil',
              style: AppFonts.semiBoldTextStyle(
                baseFontSize,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
