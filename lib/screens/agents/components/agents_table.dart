import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'dart:math' as math;
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';
import '../../../models/agent.dart';
import '../../../config/responsive.dart';
import '../../../config/app_strings.dart';
import '../../../config/json_consts.dart';
import '/config/constants.dart';

class AgentsTable extends HookWidget {
  const AgentsTable({super.key});

  @override
  Widget build(BuildContext context) {
    final currentPage = useState(1);
    final itemsPerPage = 10;
    final searchQuery = useState('');
    final searchController = useTextEditingController();

    useEffect(() {
      void onSearchChanged() {
        searchQuery.value = searchController.text;
        currentPage.value = 1; // Reset to first page when searching
      }

      searchController.addListener(onSearchChanged);
      return () => searchController.removeListener(onSearchChanged);
    }, [searchController]);

    // Get filtered agents based on search
    final filteredAgents = agentList.where((agent) {
      return agent.name.toLowerCase().contains(searchQuery.value.toLowerCase());
    }).toList();

    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final double responsivePadding = Responsive.getResponsivePadding(context);

    return Container(
      padding: EdgeInsets.all(responsivePadding),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(isSmallMobile ? 8 : 10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context, searchController),
          SizedBox(height: responsivePadding),
          LayoutBuilder(
            builder: (context, constraints) => _buildTable(
              context,
              constraints,
              filteredAgents,
              currentPage.value,
              itemsPerPage,
              searchController,
            ),
          ),
          SizedBox(height: responsivePadding),
          _buildFooter(context, filteredAgents, currentPage, itemsPerPage),
        ],
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    TextEditingController searchController,
  ) {
    final bool isMobile = Responsive.isMobile(context);
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            agentsTab,
            style: AppFonts.semiBoldTextStyle(
              18 * fontSizeMultiplier,
              color: AppTheme.primaryTextColor,
            ),
          ),
          SizedBox(height: isSmallMobile ? 8 : 12),
          _buildSearchField(searchController, context),
        ],
      );
    }

    return Row(
      children: [
        Text(
          agentsTab,
          style: AppFonts.semiBoldTextStyle(
            18 * fontSizeMultiplier,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const Spacer(),
        SizedBox(
          width: Responsive.isTablet(context) ? 250 : 300,
          child: _buildSearchField(searchController, context),
        ),
      ],
    );
  }

  Widget _buildSearchField(TextEditingController controller, [BuildContext? context]) {
    final bool isSmallMobile = context != null ? Responsive.isSmallMobile(context) : false;
    final double fontSizeMultiplier = context != null ? Responsive.getFontSizeMultiplier(context) : 1.0;

    return Container(
      height: isSmallMobile ? 36 : 40,
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          hintText: searchHint,
          hintStyle: AppFonts.regularTextStyle(
            14 * fontSizeMultiplier,
            color: AppTheme.secondaryTextColor,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppTheme.secondaryTextColor,
            size: 20,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: defaultPadding,
            vertical: 10,
          ),
        ),
      ),
    );
  }

  Widget _buildTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> filteredAgents,
    int currentPage,
    int itemsPerPage,
    TextEditingController searchController,
  ) {
    // Calculate start and end indices for pagination
    final int startIndex = (currentPage - 1) * itemsPerPage;
    final int endIndex = math.min(
      startIndex + itemsPerPage,
      filteredAgents.length,
    );

    // Get the paginated agents list
    final List<Agent> paginatedAgents = filteredAgents.isEmpty
        ? []
        : filteredAgents.sublist(startIndex, endIndex);

    if (Responsive.isMobile(context)) {
      return _buildMobileTable(context, paginatedAgents, searchController);
    } else if (Responsive.isTablet(context)) {
      return _buildTabletTable(context, constraints, paginatedAgents);
    } else {
      return _buildDesktopTable(context, constraints, paginatedAgents);
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> paginatedAgents,
  ) {
    final bool isLargeDesktop = Responsive.isLargeDesktop(context);
    final bool isExtraLargeDesktop = Responsive.isExtraLargeDesktop(context);
    final double responsivePadding = Responsive.getResponsivePadding(context);

    // Determine minimum width based on screen size
    double minWidth = 1200;
    if (isExtraLargeDesktop) {
      minWidth = 1600;
    } else if (isLargeDesktop) {
      minWidth = 1400;
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: math.max(constraints.maxWidth, minWidth),
        child: DataTable(
          columnSpacing: responsivePadding * 0.8,
          dataRowMinHeight: isLargeDesktop ? 45 : 40,
          dataRowMaxHeight: isLargeDesktop ? 55 : 50,
          columns: [
            _dataColumn(name: agentName),
            _dataColumn(name: agentContact),
            _dataColumn(name: agentEmail),
            _dataColumn(name: agentJoinDate),
            _dataColumn(name: agentState),
            _dataColumn(name: agentCity),
            _dataColumn(name: agentLevel),
            _dataColumn(name: agentTotalDeals),
            _dataColumn(name: agentEarning),
            _dataColumn(name: agentStatus),
            _dataColumn(name: actions, allowSort: false),
          ],
          rows: List.generate(
            paginatedAgents.length,
            (index) => _agentDesktopDataRow(context, paginatedAgents[index]),
          ),
        ),
      ),
    );
  }

  Widget _buildTabletTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> paginatedAgents,
  ) {
    final double responsivePadding = Responsive.getResponsivePadding(context);
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    // Adjust minimum width for tablet
    final double minWidth = constraints.maxWidth > 900 ? 1100 : 900;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: math.max(constraints.maxWidth, minWidth),
        child: DataTable(
          columnSpacing: responsivePadding * 0.5,
          dataRowMinHeight: 46,
          dataRowMaxHeight: 50,
          columns: [
            _dataColumn(name: agentName, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentContact, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentEmail, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentJoinDate, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentState, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentCity, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentLevel, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentTotalDeals, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentEarning, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(name: agentStatus, fontSize: 12 * fontSizeMultiplier),
            _dataColumn(
              name: '      $actionsColumnHeader      ',
              fontSize: 12 * fontSizeMultiplier,
              allowSort: false,
            ),
          ],
          rows: List.generate(
            paginatedAgents.length,
            (index) => _agentTabletDataRow(
              context,
              paginatedAgents[index],
              constraints,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileTable(
    BuildContext context,
    List<Agent> paginatedAgents,
    TextEditingController searchController,
  ) {
    final double responsivePadding = Responsive.getResponsivePadding(context);
    final bool isSmallMobile = Responsive.isSmallMobile(context);

    return Column(
      children: [
        // Search field is now handled in header for mobile
        ...List.generate(
          paginatedAgents.length,
          (index) => Padding(
            padding: EdgeInsets.only(bottom: responsivePadding),
            child: _agentMobileCard(context, paginatedAgents[index]),
          ),
        ),
        if (paginatedAgents.isEmpty)
          Container(
            padding: EdgeInsets.all(responsivePadding * 2),
            child: Text(
              'No agents found',
              style: AppFonts.regularTextStyle(
                14,
                color: AppTheme.secondaryTextColor,
              ),
            ),
          ),
      ],
    );
  }

  DataColumn _dataColumn({
    String name = '',
    bool allowSort = true,
    double fontSize = 14,
  }) {
    return DataColumn(
      label: Expanded(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (name != '')
              Expanded(
                child: Text(
                  name,
                  maxLines: 2,
                  style: AppFonts.mediumTextStyle(
                    fontSize,
                    color: AppTheme.tableColumnHeaderColor,
                  ),
                ),
              ),
            if (allowSort)
              Image.asset(
                '$iconAssetpath/column_sort.png',
                height: 16,
                width: 16,
              ),
          ],
        ),
      ),
    );
  }

  DataRow _agentDesktopDataRow(BuildContext context, Agent agent) {
    return DataRow(
      cells: [
        DataCell(
          _AgentNameCell(
            name: agent.name,
            imageUrl: agent.imageUrl,
            onViewPressed: () {},
            isCompact: false,
            isMobile: false,
          ),
        ),
        DataCell(Text(agent.contact, overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.email, overflow: TextOverflow.ellipsis)),
        DataCell(Text(_getJoinDate(agent), overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.state, overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.city, overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.level, overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.totalDeals.toString(), overflow: TextOverflow.ellipsis)),
        DataCell(
          Text(_formatCurrency(agent.earning), overflow: TextOverflow.ellipsis),
        ),
        DataCell(_StatusIndicator(isActive: agent.status)),
        DataCell(
          _ActionButton(
            onPressed: () => _showAgentDetails(agent),
            isCompact: true,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  DataRow _agentTabletDataRow(
    BuildContext context,
    Agent agent,
    BoxConstraints constraints,
  ) {
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);
    final double baseFontSize = 12 * fontSizeMultiplier;

    return DataRow(
      cells: [
        DataCell(
          _AgentNameCell(
            name: agent.name,
            imageUrl: agent.imageUrl,
            onViewPressed: () {},
            isCompact: true,
            isMobile: false,
          ),
        ),
        _dataCell(agent.contact, TextOverflow.ellipsis, fontSize: baseFontSize),
        _dataCell(agent.email, TextOverflow.ellipsis, fontSize: baseFontSize),
        _dataCell(_getJoinDate(agent), TextOverflow.ellipsis, fontSize: baseFontSize),
        _dataCell(agent.state, TextOverflow.ellipsis, fontSize: baseFontSize),
        _dataCell(agent.city, TextOverflow.ellipsis, fontSize: baseFontSize),
        _dataCell(agent.level, TextOverflow.ellipsis, fontSize: baseFontSize),
        _dataCell(agent.totalDeals.toString(), TextOverflow.ellipsis, fontSize: baseFontSize),
        _dataCell(
          _formatCurrency(agent.earning),
          TextOverflow.ellipsis,
          fontSize: baseFontSize,
        ),
        DataCell(_StatusIndicator(isActive: agent.status)),
        DataCell(
          _ActionButton(
            onPressed: () => _showAgentDetails(agent),
            isCompact: true,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  Widget _agentMobileCard(BuildContext context, Agent agent) {
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final double responsivePadding = Responsive.getResponsivePadding(context);
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    return Container(
      padding: EdgeInsets.all(responsivePadding),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(isSmallMobile ? 6 : 8),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _AgentNameCell(
            name: agent.name,
            imageUrl: agent.imageUrl,
            onViewPressed: () {},
            isMobile: true,
          ),
          SizedBox(height: responsivePadding * 0.75),
          _buildMobileCardRow(agentContact, agent.contact, context),
          _buildMobileCardRow(agentEmail, agent.email, context),
          _buildMobileCardRow(agentJoinDate, _getJoinDate(agent), context),
          _buildMobileCardRow(agentState, agent.state, context),
          _buildMobileCardRow(agentCity, agent.city, context),
          _buildMobileCardRow(agentLevel, agent.level, context),
          _buildMobileCardRow(agentTotalDeals, agent.totalDeals.toString(), context),
          _buildMobileCardRow(agentEarning, _formatCurrency(agent.earning), context),
          SizedBox(height: responsivePadding * 0.5),
          Row(
            children: [
              Text(
                "Status: ",
                style: AppFonts.mediumTextStyle(
                  12 * fontSizeMultiplier,
                  color: AppTheme.secondaryTextColor,
                ),
              ),
              _StatusIndicator(isActive: agent.status),
            ],
          ),
          SizedBox(height: responsivePadding * 0.75),
          SizedBox(
            width: double.infinity,
            child: _ActionButton(
              onPressed: () => _showAgentDetails(agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileCardRow(String label, String value, [BuildContext? context]) {
    final bool isSmallMobile = context != null ? Responsive.isSmallMobile(context) : false;
    final double fontSizeMultiplier = context != null ? Responsive.getFontSizeMultiplier(context) : 1.0;
    final double responsivePadding = context != null ? Responsive.getResponsivePadding(context) : 16.0;

    return Padding(
      padding: EdgeInsets.only(bottom: responsivePadding * 0.5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: isSmallMobile ? 80 : 100,
            child: Text(
              "$label:",
              style: AppFonts.mediumTextStyle(
                12 * fontSizeMultiplier,
                color: AppTheme.secondaryTextColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppFonts.regularTextStyle(
                12 * fontSizeMultiplier,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  DataCell _dataCell(
    String value,
    TextOverflow overflow, {
    double fontSize = 14,
  }) => DataCell(
    Text(
      value,
      overflow: overflow,
      style: AppFonts.mediumTextStyle(
        fontSize,
        color: AppTheme.primaryTextColor,
      ),
    ),
  );

  // Helper methods for agent data
  String _getJoinDate(Agent agent) {
    // Since the Agent model doesn't have join date, we'll use sample dates
    final sampleDates = [
      "06/28/2025", "07/03/2025", "05/20/2025", "06/10/2025", "07/01/2025",
      "04/18/2025", "06/30/2025", "05/05/2025", "05/12/2025", "07/06/2025",
      "03/15/2025", "08/22/2025", "09/10/2025", "02/28/2025"
    ];
    return sampleDates[agentList.indexOf(agent) % sampleDates.length];
  }



  String _formatCurrency(double amount) {
    return "\$${amount.toStringAsFixed(2).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}";
  }

  void _showAgentDetails(Agent agent) {
    // TODO: Implement agent details view
    // For now, this is a placeholder for the agent details functionality
  }

  Widget _buildFooter(
    BuildContext context,
    List<Agent> filteredAgents,
    ValueNotifier<int> currentPage,
    int itemsPerPage,
  ) {
    final bool isMobile = Responsive.isMobile(context);
    final double responsivePadding = Responsive.getResponsivePadding(context);

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _showingDataText(
            context,
            filteredAgents,
            currentPage.value,
            itemsPerPage,
          ),
          SizedBox(height: responsivePadding),
          _buildPagination(context, filteredAgents, currentPage, itemsPerPage),
        ],
      );
    }

    return Row(
      children: [
        _showingDataText(
          context,
          filteredAgents,
          currentPage.value,
          itemsPerPage,
        ),
        const Spacer(),
        _buildPagination(context, filteredAgents, currentPage, itemsPerPage),
      ],
    );
  }

  Widget _showingDataText(
    BuildContext context,
    List<Agent> filteredAgents,
    int currentPage,
    int itemsPerPage,
  ) {
    final int startIndex = (currentPage - 1) * itemsPerPage + 1;
    final int endIndex = math.min(
      currentPage * itemsPerPage,
      filteredAgents.length,
    );
    final int totalEntries = filteredAgents.length;

    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    return Text(
      "$showingDataLabelP1 $startIndex $toLabel $endIndex $ofLabel $totalEntries $showingDataLabelP2",
      style: AppFonts.regularTextStyle(
        14 * fontSizeMultiplier,
        color: AppTheme.pageSummaryLabelColor,
      ),
    );
  }

  Widget _buildPagination(
    BuildContext context,
    List<Agent> filteredAgents,
    ValueNotifier<int> currentPage,
    int itemsPerPage,
  ) {
    // Calculate total pages based on agent length
    final int totalPages = (filteredAgents.length / itemsPerPage).ceil();

    // Create pagination buttons based on current page and total pages
    List<Widget> paginationButtons = [];

    // Add left arrow
    paginationButtons.add(
      _paginationButton(
        icon: Icons.chevron_left,
        onTap: currentPage.value > 1 ? () => currentPage.value-- : null,
      ),
    );

    // Add page numbers
    if (Responsive.isMobile(context) || totalPages <= 5) {
      // For mobile or few pages, show all pages or just 3
      final int pagesToShow = Responsive.isMobile(context) ? 3 : totalPages;
      for (int i = 1; i <= pagesToShow; i++) {
        paginationButtons.add(
          _paginationButton(
            label: "$i",
            isSelected: i == currentPage.value,
            onTap: () => currentPage.value = i,
          ),
        );
      }
    } else {
      // For desktop with many pages, show current page with neighbors and ellipsis
      // Always show first page
      paginationButtons.add(
        _paginationButton(
          label: "1",
          isSelected: 1 == currentPage.value,
          onTap: () => currentPage.value = 1,
        ),
      );

      if (currentPage.value > 3) {
        paginationButtons.add(_paginationButton(label: "...", onTap: null));
      }

      // Show current page and neighbors
      for (
        int i = math.max(2, currentPage.value - 1);
        i <= math.min(totalPages - 1, currentPage.value + 1);
        i++
      ) {
        paginationButtons.add(
          _paginationButton(
            label: "$i",
            isSelected: i == currentPage.value,
            onTap: () => currentPage.value = i,
          ),
        );
      }

      if (currentPage.value < totalPages - 2) {
        paginationButtons.add(_paginationButton(label: "...", onTap: null));
      }

      // Always show last page
      if (totalPages > 1) {
        paginationButtons.add(
          _paginationButton(
            label: "$totalPages",
            isSelected: totalPages == currentPage.value,
            onTap: () => currentPage.value = totalPages,
          ),
        );
      }
    }

    // Add right arrow
    paginationButtons.add(
      _paginationButton(
        icon: Icons.chevron_right,
        onTap: currentPage.value < totalPages
            ? () => currentPage.value++
            : null,
      ),
    );

    return Wrap(spacing: 8, children: paginationButtons);
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.paginationActiveBg
              : AppTheme.paginationInactiveBg,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: icon != null
              ? Icon(
                  icon,
                  color: isSelected
                      ? AppTheme.white
                      : AppTheme.primaryTextColor,
                  size: 20,
                )
              : Text(
                  label ?? '',
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: isSelected
                        ? AppTheme.white
                        : AppTheme.primaryTextColor,
                  ),
                ),
        ),
      ),
    );
  }
}

// Agent Name Cell Component
class _AgentNameCell extends StatelessWidget {
  final String name;
  final String imageUrl;
  final VoidCallback onViewPressed;
  final bool isCompact;
  final bool isMobile;

  const _AgentNameCell({
    required this.name,
    required this.imageUrl,
    required this.onViewPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CircleAvatar(
          radius: isCompact ? 16 : 20,
          backgroundImage: AssetImage(imageUrl),
          backgroundColor: AppTheme.greyRoundBg,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                name,
                style: AppFonts.mediumTextStyle(
                  isMobile ? 14 : (isCompact ? 12 : 14),
                  color: AppTheme.primaryTextColor,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              if (!isCompact && !isMobile)
                GestureDetector(
                  onTap: onViewPressed,
                  child: Text(
                    viewProfile,
                    style: AppFonts.regularTextStyle(
                      12,
                      color: AppTheme.viewMoreBlue,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}

// Status Indicator Component
class _StatusIndicator extends StatelessWidget {
  final bool isActive;

  const _StatusIndicator({required this.isActive});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: isActive
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        isActive ? "Active" : "Inactive",
        style: AppFonts.mediumTextStyle(
          12,
          color: isActive ? Colors.green : Colors.red,
        ),
      ),
    );
  }
}

// Action Button Component
class _ActionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isCompact;
  final bool isMobile;

  const _ActionButton({
    required this.onPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return TextButton.icon(
        onPressed: onPressed,
        icon: Image.asset(
          '$iconAssetpath/eye.png',
          height: isCompact ? 20 : 24,
          width: isCompact ? 20 : 24,
        ),
        label: Text(
          "View Details",
          style: AppFonts.regularTextStyle(14, color: AppTheme.blueCardColor),
        ),
      );
    }

    final iconSize = isCompact ? 12.0 : 16.0;
    final alignment = isCompact ? Alignment.centerRight : Alignment.center;
    final maxWidth = isCompact ? 60.0 : double.infinity;

    return Container(
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: isCompact ? 8 : 16,
            vertical: isCompact ? 4 : 8,
          ),
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        child: Align(
          alignment: alignment,
          child: Image.asset(
            '$iconAssetpath/eye.png',
            height: iconSize,
            width: iconSize,
          ),
        ),
      ),
    );
  }
}
