import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'dart:math' as math;
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';
import '../../../models/agent.dart';
import '../../../config/responsive.dart';
import '../../../config/app_strings.dart';
import '../../../config/json_consts.dart';
import '/config/constants.dart';

class AgentsTable extends HookWidget {
  const AgentsTable({super.key});

  @override
  Widget build(BuildContext context) {
    final currentPage = useState(1);
    final itemsPerPage = 10;
    final searchQuery = useState('');
    final searchController = useTextEditingController();

    useEffect(() {
      void onSearchChanged() {
        searchQuery.value = searchController.text;
        currentPage.value = 1; // Reset to first page when searching
      }

      searchController.addListener(onSearchChanged);
      return () => searchController.removeListener(onSearchChanged);
    }, [searchController]);

    // Get filtered agents based on search
    final filteredAgents = agentList.where((agent) {
      return agent.name.toLowerCase().contains(searchQuery.value.toLowerCase());
    }).toList();

    // Responsive checks similar to dashboard_content.dart
    final Size _size = MediaQuery.of(context).size;
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final bool isMobile = Responsive.isMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    final bool isDesktop = Responsive.isDesktop(context);

    // Responsive padding similar to dashboard_content.dart
    final double containerPadding = isSmallMobile
        ? defaultPadding * 0.5
        : isMobile
            ? defaultPadding * 0.75
            : defaultPadding;

    // Responsive border radius based on screen size
    final double borderRadius = _size.width < 600 ? 8 : 10;

    return Container(
      padding: EdgeInsets.all(containerPadding),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context, searchController),
          SizedBox(height: containerPadding),
          LayoutBuilder(
            builder: (context, constraints) => _buildTable(
              context,
              constraints,
              filteredAgents,
              currentPage.value,
              itemsPerPage,
              searchController,
            ),
          ),
          SizedBox(height: containerPadding),
          _buildFooter(context, filteredAgents, currentPage, itemsPerPage),
        ],
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    TextEditingController searchController,
  ) {
    // Responsive checks similar to dashboard_content.dart
    final Size size = MediaQuery.of(context).size;
    final bool isMobile = Responsive.isMobile(context);
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    // Responsive font size based on screen width (similar to dashboard pattern)
    final double titleFontSize = size.width < 600
        ? 16 * fontSizeMultiplier
        : size.width < tabletBreakpoint
            ? 17 * fontSizeMultiplier
            : 18 * fontSizeMultiplier;

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            agentsTab,
            style: AppFonts.semiBoldTextStyle(
              titleFontSize,
              color: AppTheme.primaryTextColor,
            ),
          ),
          SizedBox(height: isSmallMobile ? defaultPadding * 0.5 : defaultPadding * 0.75),
          _buildSearchField(searchController, context),
        ],
      );
    }

    return Row(
      children: [
        Text(
          agentsTab,
          style: AppFonts.semiBoldTextStyle(
            titleFontSize,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const Spacer(),
        SizedBox(
          width: isTablet ? 250 : size.width > desktopBreakpoint ? 350 : 300,
          child: _buildSearchField(searchController, context),
        ),
      ],
    );
  }

  Widget _buildSearchField(
    TextEditingController controller, [
    BuildContext? context,
  ]) {
    // Handle null context case
    if (context == null) {
      return Container(
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.searchbarBg,
          borderRadius: BorderRadius.circular(8),
        ),
        child: TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: searchHint,
            hintStyle: AppFonts.regularTextStyle(14, color: AppTheme.secondaryTextColor),
            prefixIcon: Icon(Icons.search, color: AppTheme.secondaryTextColor, size: 20),
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: defaultPadding, vertical: 10),
          ),
        ),
      );
    }

    // Responsive checks similar to dashboard_content.dart
    final Size size = MediaQuery.of(context).size;
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    // Responsive height and font size based on screen width (similar to dashboard pattern)
    final double searchHeight = size.width < 600
        ? 36
        : size.width < tabletBreakpoint
            ? 38
            : 40;

    final double searchFontSize = size.width < 600
        ? 13 * fontSizeMultiplier
        : 14 * fontSizeMultiplier;

    final double iconSize = size.width < 600 ? 18 : 20;

    return Container(
      height: searchHeight,
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          hintText: searchHint,
          hintStyle: AppFonts.regularTextStyle(
            searchFontSize,
            color: AppTheme.secondaryTextColor,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppTheme.secondaryTextColor,
            size: iconSize,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: size.width < 600 ? defaultPadding * 0.75 : defaultPadding,
            vertical: size.width < 600 ? 8 : 10,
          ),
        ),
      ),
    );
  }

  Widget _buildTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> filteredAgents,
    int currentPage,
    int itemsPerPage,
    TextEditingController searchController,
  ) {
    // Calculate start and end indices for pagination
    final int startIndex = (currentPage - 1) * itemsPerPage;
    final int endIndex = math.min(
      startIndex + itemsPerPage,
      filteredAgents.length,
    );

    // Get the paginated agents list
    final List<Agent> paginatedAgents = filteredAgents.isEmpty
        ? []
        : filteredAgents.sublist(startIndex, endIndex);

    // Responsive table/card decision similar to dashboard_content.dart patterns
    final Size size = MediaQuery.of(context).size;

    // Use cards for mobile, table for tablet and desktop based on new breakpoints
    if (size.width < 800) { // mobileBreakpoint
      // Use mobile cards for mobile screens
      return _buildMobileTable(context, paginatedAgents, searchController);
    } else if (size.width < 1200) { // tabletBreakpoint
      // Use tablet optimized table for tablet screens
      return _buildTabletOptimizedTable(context, constraints, paginatedAgents);
    } else {
      // Use full desktop table for larger screens (>= 1200px)
      return _buildDesktopTable(context, constraints, paginatedAgents);
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> paginatedAgents,
  ) {
    // Responsive checks similar to dashboard_content.dart
    final Size size = MediaQuery.of(context).size;
    final bool isLargeDesktop = Responsive.isLargeDesktop(context);
    final bool isExtraLargeDesktop = Responsive.isExtraLargeDesktop(context);

    // Determine minimum width based on screen size to ensure all columns are visible
    double minWidth = 1200; // Base minimum width for all columns
    if (isExtraLargeDesktop) {
      minWidth = 1600;
    } else if (isLargeDesktop) {
      minWidth = 1400;
    } else if (size.width < desktopBreakpoint) {
      // For smaller desktop screens, ensure minimum width to show all columns
      minWidth = 1300;
    }

    // Responsive column spacing based on available width
    final double columnSpacing = size.width > 1400
        ? defaultPadding * 1.2
        : size.width > 1200
            ? defaultPadding * 0.8
            : defaultPadding * 0.6;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: math.max(constraints.maxWidth, minWidth),
        ),
        child: DataTable(
          columnSpacing: columnSpacing,
          dataRowMinHeight: isLargeDesktop ? 45 : 40,
          dataRowMaxHeight: isLargeDesktop ? 55 : 50,
          columns: [
            _dataColumn(name: agentName),
            _dataColumn(name: agentContact),
            _dataColumn(name: agentEmail),
            _dataColumn(name: agentJoinDate),
            _dataColumn(name: agentState),
            _dataColumn(name: agentCity),
            _dataColumn(name: agentLevel),
            _dataColumn(name: agentTotalDeals),
            _dataColumn(name: agentEarning),
            _dataColumn(name: agentStatus),
            _dataColumn(name: actions, allowSort: false),
          ],
          rows: List.generate(
            paginatedAgents.length,
            (index) => _agentDesktopDataRow(context, paginatedAgents[index]),
          ),
        ),
      ),
    );
  }

  Widget _buildTabletOptimizedTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Agent> paginatedAgents,
  ) {
    final Size size = MediaQuery.of(context).size;
    final ScrollController horizontalScrollController = ScrollController();

    // Responsive optimization based on screen width for tablet
    // Calculate minimum width to ensure all columns fit, especially Status and Actions
    double minWidth = size.width; // Use available screen width first
    double columnSpacing = 1.0; // Reasonable column spacing
    double dataRowHeight = 40.0; // Comfortable row height
    double fontSize = 11.0; // Keep consistent font size - no reduction

    // Adjust only spacing and height, keep font size consistent
    if (size.width < 1200) { // tabletBreakpoint
      columnSpacing = 0.8;
      dataRowHeight = 38.0;
    }
    if (size.width < 1000) {
      columnSpacing = 0.6;
      dataRowHeight = 36.0;
    }
    if (size.width < 900) {
      columnSpacing = 0.4;
      dataRowHeight = 34.0;
    }
    if (size.width < 850) {
      columnSpacing = 0.3;
      dataRowHeight = 32.0;
    }

    // Enable horizontal scroll for screens that need it
    // Show scrollbar from width 1200px to ensure all columns are visible
    if (size.width >= 1200) {
      minWidth = size.width; // Use full available width, no horizontal scroll
    } else if (size.width >= 800) {
      minWidth = 1200; // Enable horizontal scroll below 1200px to show all columns
    } else {
      minWidth = 800; // Enable horizontal scroll below 800px (mobile breakpoint)
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppTheme.borderColor.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Add scroll hint when horizontal scrolling is needed
          if (constraints.maxWidth < minWidth)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                children: [
                  Icon(
                    Icons.swipe_left,
                    size: 12,
                    color: AppTheme.secondaryTextColor,
                  ),
                  SizedBox(width: 4),
                  Text(
                    'Scroll horizontally to view Actions column and all data',
                    style: TextStyle(
                      fontSize: 9,
                      color: AppTheme.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
          Scrollbar(
            controller: horizontalScrollController,
            thumbVisibility: true, // Always show scrollbar for better UX
            trackVisibility: true,
            thickness: 12.0, // Make scrollbar more visible
            radius: Radius.circular(6),
            child: SingleChildScrollView(
              controller: horizontalScrollController,
              scrollDirection: Axis.horizontal,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minWidth: math.max(constraints.maxWidth, minWidth),
                ),
              child: DataTable(
                columnSpacing: columnSpacing,
                dataRowMinHeight: dataRowHeight,
                dataRowMaxHeight: dataRowHeight + 2, // Ultra compact height
                headingRowHeight: 30.0, // Compact header height
                horizontalMargin: 0.5, // Extremely minimal horizontal margin
                showCheckboxColumn: false, // Remove checkbox column to save space
                dividerThickness: 0.3, // Ultra thin dividers to save space
                headingRowColor: WidgetStateProperty.all(
                  AppTheme.white.withValues(alpha: 0.8),
                ),
                columns: [
                  _dataColumnTablet(name: agentName, fontSize: fontSize),
                  _dataColumnTablet(name: agentContact, fontSize: fontSize),
                  _dataColumnTablet(name: agentEmail, fontSize: fontSize),
                  _dataColumnTablet(name: agentJoinDate, fontSize: fontSize),
                  _dataColumnTablet(name: agentState, fontSize: fontSize),
                  _dataColumnTablet(name: agentCity, fontSize: fontSize),
                  _dataColumnTablet(name: agentLevel, fontSize: fontSize),
                  _dataColumnTablet(name: agentTotalDeals, fontSize: fontSize),
                  _dataColumnTablet(name: agentEarning, fontSize: fontSize),
                  _dataColumnTablet(name: agentStatus, fontSize: fontSize, isStatusOrAction: true),
                  _dataColumnTablet(name: actions, allowSort: false, fontSize: fontSize, isStatusOrAction: true),
                ],
                rows: List.generate(
                  paginatedAgents.length,
                  (index) => _agentTabletDataRow(context, paginatedAgents[index], fontSize),
                ),
              ),
            ),
          ),
          ),
        ],
      ),
    );
  }

  // Tablet-optimized data column with responsive font size
  DataColumn _dataColumnTablet({
    String name = '',
    bool allowSort = true,
    double fontSize = 12.0,
    bool isStatusOrAction = false, // Special handling for Status and Actions columns
  }) {
    return DataColumn(
      label: Container(
        constraints: BoxConstraints(
          maxWidth: isStatusOrAction ? 80 : 120, // More space for full header names
        ),
        child: Text(
          name, // Use full header name
          maxLines: 2, // Allow headers to wrap to 2 lines
          overflow: TextOverflow.ellipsis,
          textAlign: isStatusOrAction ? TextAlign.center : TextAlign.start,
          style: AppFonts.mediumTextStyle(
            fontSize, // Use full font size, no reduction
            color: AppTheme.tableColumnHeaderColor,
          ),
        ),
      ),
    );
  }

  // Tablet-optimized data row with responsive font size
  DataRow _agentTabletDataRow(BuildContext context, Agent agent, double fontSize) {
    return DataRow(
      cells: [
        DataCell(
          Row(
            children: [
              CircleAvatar(
                radius: fontSize * 0.6, // Smaller avatar to save space
                backgroundImage: AssetImage(agent.imageUrl),
              ),
              SizedBox(width: 2), // Reduce spacing between avatar and name
              Flexible(
                child: Container(
                  constraints: BoxConstraints(maxWidth: 100), // Limit name width
                  child: Text(
                    agent.name,
                    maxLines: 1, // Single line with ellipsis for names
                    overflow: TextOverflow.ellipsis,
                    softWrap: false, // Prevent wrapping
                    style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.w500),
                  ),
                ),
              ),
            ],
          ),
        ),
        DataCell(
          Container(
            constraints: BoxConstraints(maxWidth: 45), // Extremely compact contact width
            child: Text(
              agent.contact,
              maxLines: 1, // Single line with ellipsis for contact
              overflow: TextOverflow.ellipsis,
              softWrap: false, // Prevent wrapping
              style: TextStyle(fontSize: fontSize),
            ),
          ),
        ),
        DataCell(
          Container(
            constraints: BoxConstraints(maxWidth: 55), // Extremely compact email width
            child: Text(
              agent.email,
              maxLines: 1, // Single line with ellipsis for emails
              overflow: TextOverflow.ellipsis,
              softWrap: false, // Prevent wrapping
              style: TextStyle(fontSize: fontSize),
            ),
          ),
        ),
        DataCell(
          Container(
            constraints: BoxConstraints(maxWidth: 35), // Extremely compact join date width
            child: Text(
              _getJoinDate(agent),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              softWrap: false,
              style: TextStyle(fontSize: fontSize),
            ),
          ),
        ),
        DataCell(
          Container(
            constraints: BoxConstraints(maxWidth: 30), // Extremely compact state width
            child: Text(
              agent.state,
              maxLines: 1, // Single line with ellipsis for state
              overflow: TextOverflow.ellipsis,
              softWrap: false, // Prevent wrapping
              style: TextStyle(fontSize: fontSize),
            ),
          ),
        ),
        DataCell(
          Container(
            constraints: BoxConstraints(maxWidth: 30), // Extremely compact city width
            child: Text(
              agent.city,
              maxLines: 1, // Single line with ellipsis for city
              overflow: TextOverflow.ellipsis,
              softWrap: false, // Prevent wrapping
              style: TextStyle(fontSize: fontSize),
            ),
          ),
        ),
        DataCell(
          Container(
            constraints: BoxConstraints(maxWidth: 25), // Ultra minimal width for level
            child: Text(
              agent.level,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              softWrap: false,
              style: TextStyle(fontSize: fontSize),
            ),
          ),
        ),
        DataCell(
          Container(
            constraints: BoxConstraints(maxWidth: 25), // Ultra minimal width for deals
            child: Text(
              agent.totalDeals.toString(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              softWrap: false,
              style: TextStyle(fontSize: fontSize),
            ),
          ),
        ),
        DataCell(
          Text(
            _formatCurrency(agent.earning),
            overflow: TextOverflow.ellipsis,
            style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.bold),
          ),
        ),
        DataCell(
          Container(
            width: 25, // Ultra compact width for status
            padding: EdgeInsets.symmetric(horizontal: 0.5, vertical: 0.5), // Ultra minimal padding
            decoration: BoxDecoration(
              color: agent.status ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(2),
            ),
            child: Text(
              agent.status ? 'A' : 'I', // Single letter for ultra compact
              maxLines: 1, // Single line with ellipsis for status
              overflow: TextOverflow.ellipsis,
              softWrap: false,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: fontSize * 0.9, // Readable font size
                color: agent.status ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        DataCell(
          Container(
            width: 35, // Slightly wider to ensure visibility
            child: Center(
              child: IconButton(
                icon: Icon(Icons.visibility, size: fontSize - 1),
                onPressed: () => _showAgentDetails(agent),
                padding: EdgeInsets.all(1),
                constraints: BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
                tooltip: 'View', // Shorter tooltip
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileTable(
    BuildContext context,
    List<Agent> paginatedAgents,
    TextEditingController searchController,
  ) {
    // Responsive checks similar to dashboard_content.dart
    final Size size = MediaQuery.of(context).size;
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final bool isTablet = Responsive.isTablet(context);

    // Use different spacing for different screen sizes (similar to dashboard pattern)
    final double cardSpacing = size.width < 600
        ? defaultPadding * 0.75
        : size.width < tabletBreakpoint
            ? defaultPadding
            : defaultPadding * 1.25;

    final double emptyPadding = size.width < 600
        ? defaultPadding
        : size.width < tabletBreakpoint
            ? defaultPadding * 1.25
            : defaultPadding * 1.5;

    // Responsive font size for empty state
    final double emptyFontSize = size.width < 600
        ? 13
        : size.width < tabletBreakpoint
            ? 14
            : 16;

    return Column(
      children: [
        // Search field is now handled in header for mobile
        ...List.generate(
          paginatedAgents.length,
          (index) => Padding(
            padding: EdgeInsets.only(bottom: cardSpacing),
            child: _agentMobileCard(context, paginatedAgents[index]),
          ),
        ),
        if (paginatedAgents.isEmpty)
          Container(
            padding: EdgeInsets.all(emptyPadding),
            child: Text(
              'No agents found',
              style: AppFonts.regularTextStyle(
                emptyFontSize,
                color: AppTheme.secondaryTextColor,
              ),
            ),
          ),
      ],
    );
  }

  DataColumn _dataColumn({
    String name = '',
    bool allowSort = true,
    double fontSize = 14,
  }) {
    return DataColumn(
      label: Expanded(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (name != '')
              Expanded(
                child: Text(
                  name,
                  maxLines: 2,
                  style: AppFonts.mediumTextStyle(
                    fontSize,
                    color: AppTheme.tableColumnHeaderColor,
                  ),
                ),
              ),
            if (allowSort)
              Image.asset(
                '$iconAssetpath/column_sort.png',
                height: 16,
                width: 16,
              ),
          ],
        ),
      ),
    );
  }

  DataRow _agentDesktopDataRow(BuildContext context, Agent agent) {
    return DataRow(
      cells: [
        DataCell(
          _AgentNameCell(
            name: agent.name,
            imageUrl: agent.imageUrl,
            onViewPressed: () {},
            isCompact: false,
            isMobile: false,
          ),
        ),
        DataCell(Text(agent.contact, overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.email, overflow: TextOverflow.ellipsis)),
        DataCell(Text(_getJoinDate(agent), overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.state, overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.city, overflow: TextOverflow.ellipsis)),
        DataCell(Text(agent.level, overflow: TextOverflow.ellipsis)),
        DataCell(
          Text(agent.totalDeals.toString(), overflow: TextOverflow.ellipsis),
        ),
        DataCell(
          Text(_formatCurrency(agent.earning), overflow: TextOverflow.ellipsis),
        ),
        DataCell(_StatusIndicator(isActive: agent.status)),
        DataCell(
          _ActionButton(
            onPressed: () => _showAgentDetails(agent),
            isCompact: true,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  Widget _agentMobileCard(BuildContext context, Agent agent) {
    // Responsive checks similar to dashboard_content.dart
    final Size size = MediaQuery.of(context).size;
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    // Use constants for padding based on screen size (similar to dashboard pattern)
    final double cardPadding = size.width < 600
        ? defaultPadding * 0.75
        : size.width < tabletBreakpoint
            ? defaultPadding * 1.25
            : defaultPadding;

    // Responsive border radius
    final double borderRadius = size.width < 600 ? 6 : size.width < tabletBreakpoint ? 8 : 10;

    return Container(
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _AgentNameCell(
            name: agent.name,
            imageUrl: agent.imageUrl,
            onViewPressed: () {},
            isMobile: true,
          ),
          SizedBox(height: cardPadding * 0.75),
          _buildMobileCardRow(agentContact, agent.contact, context),
          _buildMobileCardRow(agentEmail, agent.email, context),
          _buildMobileCardRow(agentJoinDate, _getJoinDate(agent), context),
          _buildMobileCardRow(agentState, agent.state, context),
          _buildMobileCardRow(agentCity, agent.city, context),
          _buildMobileCardRow(agentLevel, agent.level, context),
          _buildMobileCardRow(
            agentTotalDeals,
            agent.totalDeals.toString(),
            context,
          ),
          _buildMobileCardRow(
            agentEarning,
            _formatCurrency(agent.earning),
            context,
          ),
          SizedBox(height: cardPadding * 0.5),
          Row(
            children: [
              Text(
                "Status: ",
                style: AppFonts.mediumTextStyle(
                  12 * fontSizeMultiplier,
                  color: AppTheme.secondaryTextColor,
                ),
              ),
              _StatusIndicator(isActive: agent.status),
            ],
          ),
          SizedBox(height: cardPadding * 0.75),
          SizedBox(
            width: double.infinity,
            child: _ActionButton(
              onPressed: () => _showAgentDetails(agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileCardRow(
    String label,
    String value, [
    BuildContext? context,
  ]) {
    // Handle null context case
    if (context == null) {
      return Padding(
        padding: EdgeInsets.only(bottom: defaultPadding * 0.375),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 100,
              child: Text(
                "$label:",
                style: AppFonts.mediumTextStyle(12, color: AppTheme.secondaryTextColor),
              ),
            ),
            Expanded(
              child: Text(
                value,
                style: AppFonts.regularTextStyle(12, color: AppTheme.primaryTextColor),
              ),
            ),
          ],
        ),
      );
    }

    // Responsive checks similar to dashboard_content.dart
    final Size size = MediaQuery.of(context).size;
    final bool isSmallMobile = Responsive.isSmallMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    // Use constants for spacing (similar to dashboard pattern)
    final double rowSpacing = size.width < 600
        ? defaultPadding * 0.25
        : size.width < tabletBreakpoint
            ? defaultPadding * 0.5
            : defaultPadding * 0.375;

    // Responsive label width and font sizes
    final double labelWidth = size.width < 600
        ? 80
        : size.width < tabletBreakpoint
            ? 120
            : 100;

    final double labelFontSize = size.width < 600
        ? 11 * fontSizeMultiplier
        : 12 * fontSizeMultiplier;

    final double valueFontSize = size.width < 600
        ? 11 * fontSizeMultiplier
        : 12 * fontSizeMultiplier;

    return Padding(
      padding: EdgeInsets.only(bottom: rowSpacing),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: labelWidth,
            child: Text(
              "$label:",
              style: AppFonts.mediumTextStyle(
                labelFontSize,
                color: AppTheme.secondaryTextColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppFonts.regularTextStyle(
                valueFontSize,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
  // Helper methods for agent data
  String _getJoinDate(Agent agent) {
    // Format the joinDate from the agent model
    final DateTime joinDate = agent.joinDate;
    return "${joinDate.month.toString().padLeft(2, '0')}/${joinDate.day.toString().padLeft(2, '0')}/${joinDate.year}";
  }

  String _formatCurrency(double amount) {
    return "\$${amount.toStringAsFixed(2).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}";
  }

  void _showAgentDetails(Agent agent) {
    // TODO: Implement agent details view
    // For now, this is a placeholder for the agent details functionality
  }
  Widget _buildFooter(
    BuildContext context,
    List<Agent> filteredAgents,
    ValueNotifier<int> currentPage,
    int itemsPerPage,
  ) {
    final bool isMobile = Responsive.isMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    final bool isSmallMobile = Responsive.isSmallMobile(context);

    // Use constants for spacing
    final double footerSpacing = isSmallMobile
        ? defaultPadding * 0.75
        : isTablet
            ? defaultPadding * 1.25
            : defaultPadding;

    if (isMobile || isTablet) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _showingDataText(
            context,
            filteredAgents,
            currentPage.value,
            itemsPerPage,
          ),
          SizedBox(height: footerSpacing),
          // Center the pagination for tablet and mobile
          Center(
            child: _buildPagination(context, filteredAgents, currentPage, itemsPerPage),
          ),
        ],
      );
    }

    return Row(
      children: [
        _showingDataText(
          context,
          filteredAgents,
          currentPage.value,
          itemsPerPage,
        ),
        const Spacer(),
        _buildPagination(context, filteredAgents, currentPage, itemsPerPage),
      ],
    );
  }

  Widget _showingDataText(
    BuildContext context,
    List<Agent> filteredAgents,
    int currentPage,
    int itemsPerPage,
  ) {
    final int startIndex = (currentPage - 1) * itemsPerPage + 1;
    final int endIndex = math.min(
      currentPage * itemsPerPage,
      filteredAgents.length,
    );
    final int totalEntries = filteredAgents.length;

    final double fontSizeMultiplier = Responsive.getFontSizeMultiplier(context);

    return Text(
      "$showingDataLabelP1 $startIndex $toLabel $endIndex $ofLabel $totalEntries $showingDataLabelP2",
      style: AppFonts.regularTextStyle(
        14 * fontSizeMultiplier,
        color: AppTheme.pageSummaryLabelColor,
      ),
    );
  }

  Widget _buildPagination(
    BuildContext context,
    List<Agent> filteredAgents,
    ValueNotifier<int> currentPage,
    int itemsPerPage,
  ) {
    // Calculate total pages based on agent length
    final int totalPages = (filteredAgents.length / itemsPerPage).ceil();

    // Create pagination buttons based on current page and total pages
    List<Widget> paginationButtons = [];

    // Add left arrow
    paginationButtons.add(
      _paginationButton(
        icon: Icons.chevron_left,
        onTap: currentPage.value > 1 ? () => currentPage.value-- : null,
      ),
    );

    // Add page numbers
    if (Responsive.isMobile(context) || totalPages <= 5) {
      // For mobile or few pages, show all pages or just 3
      final int pagesToShow = Responsive.isMobile(context) ? 3 : totalPages;
      for (int i = 1; i <= pagesToShow; i++) {
        paginationButtons.add(
          _paginationButton(
            label: "$i",
            isSelected: i == currentPage.value,
            onTap: () => currentPage.value = i,
          ),
        );
      }
    } else {
      // For desktop with many pages, show current page with neighbors and ellipsis
      // Always show first page
      paginationButtons.add(
        _paginationButton(
          label: "1",
          isSelected: 1 == currentPage.value,
          onTap: () => currentPage.value = 1,
        ),
      );

      if (currentPage.value > 3) {
        paginationButtons.add(_paginationButton(label: "...", onTap: null));
      }

      // Show current page and neighbors
      for (
        int i = math.max(2, currentPage.value - 1);
        i <= math.min(totalPages - 1, currentPage.value + 1);
        i++
      ) {
        paginationButtons.add(
          _paginationButton(
            label: "$i",
            isSelected: i == currentPage.value,
            onTap: () => currentPage.value = i,
          ),
        );
      }

      if (currentPage.value < totalPages - 2) {
        paginationButtons.add(_paginationButton(label: "...", onTap: null));
      }

      // Always show last page
      if (totalPages > 1) {
        paginationButtons.add(
          _paginationButton(
            label: "$totalPages",
            isSelected: totalPages == currentPage.value,
            onTap: () => currentPage.value = totalPages,
          ),
        );
      }
    }

    // Add right arrow
    paginationButtons.add(
      _paginationButton(
        icon: Icons.chevron_right,
        onTap: currentPage.value < totalPages
            ? () => currentPage.value++
            : null,
      ),
    );

    return Wrap(spacing: 8, children: paginationButtons);
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.paginationActiveBg
              : AppTheme.paginationInactiveBg,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: icon != null
              ? Icon(
                  icon,
                  color: isSelected
                      ? AppTheme.white
                      : AppTheme.primaryTextColor,
                  size: 20,
                )
              : Text(
                  label ?? '',
                  style: AppFonts.mediumTextStyle(
                    14,
                    color: isSelected
                        ? AppTheme.white
                        : AppTheme.primaryTextColor,
                  ),
                ),
        ),
      ),
    );
  }
}
// Agent Name Cell Component
class _AgentNameCell extends StatelessWidget {
  final String name;
  final String imageUrl;
  final VoidCallback onViewPressed;
  final bool isCompact;
  final bool isMobile;

  const _AgentNameCell({
    required this.name,
    required this.imageUrl,
    required this.onViewPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CircleAvatar(
          radius: isCompact ? 16 : 20,
          backgroundImage: AssetImage(imageUrl),
          backgroundColor: AppTheme.greyRoundBg,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                name,
                style: AppFonts.mediumTextStyle(
                  isMobile ? 14 : (isCompact ? 12 : 14),
                  color: AppTheme.primaryTextColor,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              if (!isCompact && !isMobile)
                GestureDetector(
                  onTap: onViewPressed,
                  child: Text(
                    viewProfile,
                    style: AppFonts.regularTextStyle(
                      12,
                      color: AppTheme.viewMoreBlue,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
// Status Indicator Component
class _StatusIndicator extends StatelessWidget {
  final bool isActive;

  const _StatusIndicator({required this.isActive});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: isActive
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        isActive ? "Active" : "Inactive",
        style: AppFonts.mediumTextStyle(
          12,
          color: isActive ? Colors.green : Colors.red,
        ),
      ),
    );
  }
}
// Action Button Component
class _ActionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isCompact;
  final bool isMobile;

  const _ActionButton({
    required this.onPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return TextButton.icon(
        onPressed: onPressed,
        icon: Image.asset(
          '$iconAssetpath/eye.png',
          height: isCompact ? 20 : 24,
          width: isCompact ? 20 : 24,
        ),
        label: Text(
          "View Details",
          style: AppFonts.regularTextStyle(14, color: AppTheme.blueCardColor),
        ),
      );
    }

    final iconSize = isCompact ? 12.0 : 16.0;
    final alignment = isCompact ? Alignment.centerRight : Alignment.center;
    final maxWidth = isCompact ? 60.0 : double.infinity;

    return Container(
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: isCompact ? 8 : 16,
            vertical: isCompact ? 4 : 8,
          ),
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        child: Align(
          alignment: alignment,
          child: Image.asset(
            '$iconAssetpath/eye.png',
            height: iconSize,
            width: iconSize,
          ),
        ),
      ),
    );
  }
}
